FROM php:8.2-fpm

# Arguments définis dans docker-compose.yml
ARG user=clockin
ARG uid=1000

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    supervisor

# Nettoyer le cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Installer les extensions PHP
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Installer Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Créer un utilisateur système pour exécuter les commandes Composer et Artisan
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

# Configurer Supervisor
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Définir le répertoire de travail
WORKDIR /var/www

# Copier les fichiers d'application existants
COPY . /var/www

# Copier les permissions existantes
COPY --chown=$user:$user . /var/www

# Exposer le port 9000 et démarrer php-fpm
EXPOSE 9000
CMD ["php-fpm"]
