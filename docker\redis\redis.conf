# Configuration Redis optimisée pour ClockIn

# <PERSON><PERSON><PERSON>
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# Mémoire
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistance
save 900 1
save 300 10
save 60 10000

# Logs
loglevel notice
logfile ""

# Sécurité
protected-mode no

# Performance
tcp-backlog 511
databases 16

# Optimisations
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# Clients
maxclients 10000

# Éviction
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
