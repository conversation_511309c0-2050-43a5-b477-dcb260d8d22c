<?php

namespace App\Services;

use InvalidArgumentException;

/**
 * Service pour les calculs de géolocalisation
 * Utilise la formule de Haversine pour calculer les distances entre coordonnées GPS
 */
class LocationService
{
    /**
     * Rayon de la Terre en mètres
     */
    private const EARTH_RADIUS = 6371000;

    /**
     * Calcule la distance entre deux points GPS en utilisant la formule de Haversine
     * 
     * @param float $lat1 Latitude du premier point
     * @param float $lon1 Longitude du premier point
     * @param float $lat2 Latitude du second point
     * @param float $lon2 Longitude du second point
     * @return float Distance en mètres
     * @throws InvalidArgumentException Si les coordonnées sont invalides
     */
    public function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // Validation des coordonnées
        $this->validateCoordinates($lat1, $lon1);
        $this->validateCoordinates($lat2, $lon2);

        // Conversion des degrés en radians
        $lat1Rad = deg2rad($lat1);
        $lon1Rad = deg2rad($lon1);
        $lat2Rad = deg2rad($lat2);
        $lon2Rad = deg2rad($lon2);

        // Calcul des différences
        $deltaLat = $lat2Rad - $lat1Rad;
        $deltaLon = $lon2Rad - $lon1Rad;

        // Formule de Haversine
        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLon / 2) * sin($deltaLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        // Distance en mètres
        return self::EARTH_RADIUS * $c;
    }

    /**
     * Vérifie si un point est dans un rayon donné par rapport à un point de référence
     * 
     * @param float $refLat Latitude de référence
     * @param float $refLon Longitude de référence
     * @param float $pointLat Latitude du point à vérifier
     * @param float $pointLon Longitude du point à vérifier
     * @param float $radiusMeters Rayon en mètres
     * @return bool True si le point est dans le rayon
     */
    public function isWithinRadius(
        float $refLat, 
        float $refLon, 
        float $pointLat, 
        float $pointLon, 
        float $radiusMeters
    ): bool {
        $distance = $this->calculateDistance($refLat, $refLon, $pointLat, $pointLon);
        return $distance <= $radiusMeters;
    }

    /**
     * Calcule le bearing (direction) entre deux points GPS
     * 
     * @param float $lat1 Latitude du premier point
     * @param float $lon1 Longitude du premier point
     * @param float $lat2 Latitude du second point
     * @param float $lon2 Longitude du second point
     * @return float Bearing en degrés (0-360)
     */
    public function calculateBearing(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $this->validateCoordinates($lat1, $lon1);
        $this->validateCoordinates($lat2, $lon2);

        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLonRad = deg2rad($lon2 - $lon1);

        $y = sin($deltaLonRad) * cos($lat2Rad);
        $x = cos($lat1Rad) * sin($lat2Rad) - sin($lat1Rad) * cos($lat2Rad) * cos($deltaLonRad);

        $bearingRad = atan2($y, $x);
        $bearingDeg = rad2deg($bearingRad);

        // Normaliser entre 0 et 360 degrés
        return fmod($bearingDeg + 360, 360);
    }

    /**
     * Valide les coordonnées GPS
     * 
     * @param float $latitude Latitude à valider
     * @param float $longitude Longitude à valider
     * @throws InvalidArgumentException Si les coordonnées sont invalides
     */
    private function validateCoordinates(float $latitude, float $longitude): void
    {
        if ($latitude < -90 || $latitude > 90) {
            throw new InvalidArgumentException("Latitude invalide: {$latitude}. Doit être entre -90 et 90.");
        }

        if ($longitude < -180 || $longitude > 180) {
            throw new InvalidArgumentException("Longitude invalide: {$longitude}. Doit être entre -180 et 180.");
        }
    }

    /**
     * Formate une distance en mètres avec l'unité appropriée
     * 
     * @param float $distanceMeters Distance en mètres
     * @return string Distance formatée avec unité
     */
    public function formatDistance(float $distanceMeters): string
    {
        if ($distanceMeters < 1000) {
            return round($distanceMeters, 1) . ' m';
        } else {
            return round($distanceMeters / 1000, 2) . ' km';
        }
    }

    /**
     * Calcule la précision approximative en mètres pour une coordonnée GPS
     * basée sur le nombre de décimales
     * 
     * @param float $coordinate Coordonnée GPS
     * @return float Précision approximative en mètres
     */
    public function getCoordinatePrecision(float $coordinate): float
    {
        $decimalPlaces = strlen(substr(strrchr($coordinate, "."), 1));
        
        // Approximation de la précision basée sur les décimales
        switch ($decimalPlaces) {
            case 0: return 111000; // ~111 km
            case 1: return 11100;  // ~11.1 km
            case 2: return 1110;   // ~1.1 km
            case 3: return 111;    // ~111 m
            case 4: return 11.1;   // ~11.1 m
            case 5: return 1.11;   // ~1.11 m
            case 6: return 0.111;  // ~11.1 cm
            default: return 0.01;  // ~1 cm ou mieux
        }
    }
}
