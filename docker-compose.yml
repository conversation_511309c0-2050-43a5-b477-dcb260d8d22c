version: '3.8'

services:
  # Application Laravel
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: clockin_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - clockin_network
    depends_on:
      - mysql
      - redis

  # Serveur Web Nginx
  nginx:
    image: nginx:alpine
    container_name: clockin_nginx
    restart: unless-stopped
    ports:
      - "8000:80"
      - "8443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - clockin_network
    depends_on:
      - app

  # Base de données MySQL
  mysql:
    image: mysql:8.0
    container_name: clockin_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: clockin_db
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: clockin_user
      MYSQL_PASSWORD: clockin_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - clockin_network

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: clockin_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - clockin_network

  # phpMyAdmin pour gestion BD
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: clockin_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_USER: clockin_user
      PMA_PASSWORD: clockin_password
    ports:
      - "8080:80"
    networks:
      - clockin_network
    depends_on:
      - mysql

  # Redis Commander pour gestion cache
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: clockin_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - clockin_network
    depends_on:
      - redis

  # Mailhog pour tests d'emails
  mailhog:
    image: mailhog/mailhog
    container_name: clockin_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - clockin_network

networks:
  clockin_network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
