[mysqld]
# Configuration MySQL optimisée pour ClockIn

# Paramètres généraux
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Optimisations InnoDB
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# Optimisations des requêtes
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# Connexions
max_connections = 200
max_connect_errors = 1000
wait_timeout = 600
interactive_timeout = 600

# Logs
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Sécurité
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
