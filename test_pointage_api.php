<?php
/**
 * Script de test pour l'API de pointage ClockIn
 * Teste les corrections apportées au service LocationService
 */

// Configuration
$baseUrl = 'http://127.0.0.1:8000/api';
$adminCredentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

// Coordonnées de test (Casablanca, Maroc)
$testCoordinates = [
    'latitude' => 33.5731,
    'longitude' => -7.5898
];

echo "🧪 Test de l'API de Pointage ClockIn\n";
echo "=====================================\n\n";

/**
 * Fonction pour faire des requêtes HTTP
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers),
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("Erreur cURL: $error");
    }
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

try {
    // 1. Test de connexion
    echo "1️⃣  Test de connexion...\n";
    $loginResponse = makeRequest("$baseUrl/auth/login", 'POST', $adminCredentials);
    
    if ($loginResponse['status'] !== 200) {
        throw new Exception("Échec de la connexion: " . $loginResponse['raw']);
    }
    
    $token = $loginResponse['body']['token'] ?? null;
    if (!$token) {
        throw new Exception("Token non reçu dans la réponse de connexion");
    }
    
    echo "✅ Connexion réussie\n";
    echo "   Token: " . substr($token, 0, 20) . "...\n\n";
    
    $authHeaders = ["Authorization: Bearer $token"];
    
    // 2. Test de vérification de localisation
    echo "2️⃣  Test de vérification de localisation...\n";
    $locationResponse = makeRequest(
        "$baseUrl/pointage/check-location", 
        'POST', 
        $testCoordinates, 
        $authHeaders
    );
    
    echo "   Status: {$locationResponse['status']}\n";
    echo "   Réponse: " . json_encode($locationResponse['body'], JSON_PRETTY_PRINT) . "\n\n";
    
    // 3. Test de pointage (si la localisation est OK)
    if ($locationResponse['status'] === 200 && 
        isset($locationResponse['body']['can_pointe']) && 
        $locationResponse['body']['can_pointe']) {
        
        echo "3️⃣  Test de pointage...\n";
        $pointageResponse = makeRequest(
            "$baseUrl/pointage", 
            'POST', 
            $testCoordinates, 
            $authHeaders
        );
        
        echo "   Status: {$pointageResponse['status']}\n";
        echo "   Réponse: " . json_encode($pointageResponse['body'], JSON_PRETTY_PRINT) . "\n\n";
        
        // 4. Test de fin de pointage (si le début a réussi)
        if ($pointageResponse['status'] === 201) {
            echo "4️⃣  Test de fin de pointage...\n";
            sleep(2); // Attendre 2 secondes pour avoir une durée
            
            $endPointageResponse = makeRequest(
                "$baseUrl/pointage", 
                'POST', 
                $testCoordinates, 
                $authHeaders
            );
            
            echo "   Status: {$endPointageResponse['status']}\n";
            echo "   Réponse: " . json_encode($endPointageResponse['body'], JSON_PRETTY_PRINT) . "\n\n";
        }
    } else {
        echo "⚠️  Localisation non autorisée pour le pointage\n";
        echo "   Distance: " . ($locationResponse['body']['distance'] ?? 'N/A') . " m\n";
        echo "   Site: " . ($locationResponse['body']['site'] ?? 'N/A') . "\n\n";
    }
    
    // 5. Test de liste des pointages (admin)
    echo "5️⃣  Test de liste des pointages...\n";
    $listResponse = makeRequest("$baseUrl/pointage", 'GET', null, $authHeaders);
    
    echo "   Status: {$listResponse['status']}\n";
    if ($listResponse['status'] === 200) {
        $pointagesCount = count($listResponse['body']['data'] ?? []);
        echo "   Nombre de pointages: $pointagesCount\n";
    } else {
        echo "   Erreur: " . json_encode($listResponse['body'], JSON_PRETTY_PRINT) . "\n";
    }
    
    echo "\n🎉 Tests terminés avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur lors des tests: " . $e->getMessage() . "\n";
    echo "\n📋 Vérifications à effectuer:\n";
    echo "   - Le serveur Laravel est-il démarré? (php artisan serve)\n";
    echo "   - La base de données est-elle configurée?\n";
    echo "   - Les migrations ont-elles été exécutées?\n";
    echo "   - Les données de test sont-elles présentes? (php artisan db:seed)\n";
}

echo "\n📊 Informations de débogage:\n";
echo "   URL de base: $baseUrl\n";
echo "   Coordonnées de test: " . json_encode($testCoordinates) . "\n";
echo "   Documentation API: http://127.0.0.1:8000/docs\n";
echo "   Logs Laravel: storage/logs/laravel.log\n";
